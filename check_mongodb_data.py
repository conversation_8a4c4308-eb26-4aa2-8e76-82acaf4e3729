#!/usr/bin/env python3
"""
Script to check MongoDB data storage location and show all stored grievances
"""

from pymongo import MongoClient
from datetime import datetime
import json

def show_mongodb_info():
    """Show MongoDB connection and database information"""
    print("🗄️  MONGODB DATA STORAGE INFORMATION")
    print("=" * 60)
    
    # MongoDB connection details
    print("📍 CONNECTION DETAILS:")
    print(f"   Host: localhost")
    print(f"   Port: 27017")
    print(f"   Database Name: grievance_system")
    print(f"   Collection Name: grievances")
    print(f"   Full Connection String: mongodb://localhost:27017/")
    
    print("\n📂 DATA STORAGE LOCATION:")
    print("   Your grievance data is stored in:")
    print("   Database: 'grievance_system'")
    print("   Collection: 'grievances'")
    
    return MongoClient('mongodb://localhost:27017/')

def show_database_structure():
    """Show the structure of the grievance documents"""
    print("\n📋 DOCUMENT STRUCTURE:")
    print("   Each grievance document contains:")
    print("   • _id: MongoDB ObjectId (unique identifier)")
    print("   • name: User's full name")
    print("   • mobile: 10-digit mobile number")
    print("   • complaint_details: Description of the issue")
    print("   • status: Current status (Pending/In Progress/Resolved/Closed)")
    print("   • created_at: Timestamp when grievance was created")
    print("   • updated_at: Timestamp when grievance was last updated")

def show_all_grievances():
    """Display all grievances stored in the database"""
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['grievance_system']
        collection = db['grievances']
        
        print("\n📊 ALL STORED GRIEVANCES:")
        print("=" * 60)
        
        # Get all grievances
        grievances = list(collection.find())
        
        if not grievances:
            print("   No grievances found in the database.")
            return
        
        print(f"   Total Grievances: {len(grievances)}")
        print("\n" + "-" * 60)
        
        for i, grievance in enumerate(grievances, 1):
            print(f"\n📝 GRIEVANCE #{i}")
            print(f"   ID: {grievance['_id']}")
            print(f"   Name: {grievance.get('name', 'N/A')}")
            print(f"   Mobile: {grievance.get('mobile', 'N/A')}")
            print(f"   Status: {grievance.get('status', 'N/A')}")
            print(f"   Complaint: {grievance.get('complaint_details', 'N/A')[:100]}...")
            print(f"   Created: {grievance.get('created_at', 'N/A')}")
            print(f"   Updated: {grievance.get('updated_at', 'N/A')}")
            print("-" * 60)
        
        client.close()
        
    except Exception as e:
        print(f"❌ Error accessing database: {str(e)}")

def show_mongodb_commands():
    """Show useful MongoDB commands for manual access"""
    print("\n🔧 MONGODB COMMANDS FOR MANUAL ACCESS:")
    print("=" * 60)
    print("To access your data manually using MongoDB shell:")
    print()
    print("1. Open MongoDB shell:")
    print("   mongo")
    print()
    print("2. Switch to your database:")
    print("   use grievance_system")
    print()
    print("3. Show all collections:")
    print("   show collections")
    print()
    print("4. View all grievances:")
    print("   db.grievances.find().pretty()")
    print()
    print("5. Count total grievances:")
    print("   db.grievances.count()")
    print()
    print("6. Find grievance by mobile number:")
    print("   db.grievances.find({mobile: '9876543210'}).pretty()")
    print()
    print("7. Find grievances by status:")
    print("   db.grievances.find({status: 'Pending'}).pretty()")
    print()
    print("8. Update grievance status:")
    print("   db.grievances.updateOne({mobile: '9876543210'}, {$set: {status: 'Resolved'}})")

def show_data_backup_info():
    """Show information about backing up the data"""
    print("\n💾 DATA BACKUP INFORMATION:")
    print("=" * 60)
    print("To backup your grievance data:")
    print()
    print("1. Export to JSON:")
    print("   mongoexport --db grievance_system --collection grievances --out grievances_backup.json")
    print()
    print("2. Create database dump:")
    print("   mongodump --db grievance_system --out /path/to/backup/")
    print()
    print("3. Restore from backup:")
    print("   mongorestore --db grievance_system /path/to/backup/grievance_system/")

if __name__ == "__main__":
    try:
        # Show MongoDB information
        client = show_mongodb_info()
        
        # Show database structure
        show_database_structure()
        
        # Show all stored grievances
        show_all_grievances()
        
        # Show MongoDB commands
        show_mongodb_commands()
        
        # Show backup information
        show_data_backup_info()
        
        print("\n✅ MongoDB data information displayed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("Make sure MongoDB is running on localhost:27017")
