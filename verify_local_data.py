#!/usr/bin/env python3
"""
Script to verify local MongoDB data storage
"""

from pymongo import MongoClient
import os
from datetime import datetime

def verify_local_storage():
    """Verify that data is stored locally"""
    print("🔍 VERIFYING LOCAL MONGODB DATA STORAGE")
    print("=" * 60)
    
    # Check physical directory
    db_path = "./mongodb_data/db"
    if os.path.exists(db_path):
        print(f"✅ Local database directory exists: {os.path.abspath(db_path)}")
        
        # List database files
        files = os.listdir(db_path)
        data_files = [f for f in files if f.endswith('.wt')]
        print(f"📁 Found {len(data_files)} database files")
        
        # Show directory size
        total_size = 0
        for root, dirs, files in os.walk(db_path):
            for file in files:
                file_path = os.path.join(root, file)
                total_size += os.path.getsize(file_path)
        
        print(f"💾 Total database size: {total_size / 1024:.2f} KB")
    else:
        print(f"❌ Local database directory not found: {db_path}")
        return False
    
    # Check MongoDB connection
    try:
        print("\n🔗 Testing MongoDB connection...")
        client = MongoClient('mongodb://localhost:27018/')
        db = client['grievance_system']
        collection = db['grievances']
        
        # Get data count
        count = collection.count_documents({})
        print(f"📊 Found {count} grievances in local database")
        
        if count > 0:
            print("\n📋 STORED GRIEVANCES:")
            print("-" * 40)
            for i, doc in enumerate(collection.find(), 1):
                print(f"{i}. ID: {doc['_id']}")
                print(f"   Name: {doc.get('name', 'N/A')}")
                print(f"   Mobile: {doc.get('mobile', 'N/A')}")
                print(f"   Status: {doc.get('status', 'N/A')}")
                print(f"   Created: {doc.get('created_at', 'N/A')}")
                print("-" * 40)
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        return False

def show_storage_info():
    """Show storage location information"""
    print("\n📂 STORAGE LOCATION INFORMATION")
    print("=" * 60)
    
    current_dir = os.getcwd()
    db_path = os.path.join(current_dir, "mongodb_data", "db")
    log_path = os.path.join(current_dir, "mongodb_data", "logs")
    
    print(f"🏠 Project Directory: {current_dir}")
    print(f"🗄️  Database Files: {db_path}")
    print(f"📝 Log Files: {log_path}")
    print(f"⚙️  Config File: {os.path.join(current_dir, 'mongodb_local.conf')}")
    
    print("\n🔗 Connection Details:")
    print("   Host: localhost")
    print("   Port: 27018")
    print("   Database: grievance_system")
    print("   Collection: grievances")
    print("   Connection String: mongodb://localhost:27018/")

def check_backup_files():
    """Check for backup files"""
    print("\n💾 BACKUP FILES")
    print("=" * 60)
    
    backup_files = [f for f in os.listdir('.') if f.startswith('grievances_backup_')]
    
    if backup_files:
        print(f"Found {len(backup_files)} backup files:")
        for backup in sorted(backup_files):
            file_size = os.path.getsize(backup)
            print(f"   📄 {backup} ({file_size} bytes)")
    else:
        print("No backup files found")

if __name__ == "__main__":
    print("🔍 LOCAL MONGODB VERIFICATION TOOL")
    print("=" * 60)
    
    if verify_local_storage():
        show_storage_info()
        check_backup_files()
        
        print("\n✅ VERIFICATION SUCCESSFUL!")
        print("=" * 60)
        print("🎉 Your MongoDB data is successfully stored locally!")
        print("📂 All data files are in your project directory")
        print("🔒 Data is portable and under your control")
        print("💾 Regular backups are recommended")
        
    else:
        print("\n❌ VERIFICATION FAILED!")
        print("Please check the error messages above.")
