from pymongo import MongoClient
from datetime import datetime
import os
from dotenv import load_dotenv

load_dotenv()

# MongoDB connection - Local storage in project directory
# Using port 27018 for local MongoDB instance
client = MongoClient('mongodb://localhost:27018/')
db = client['grievance_system']
grievances_collection = db['grievances']

print(f"📂 MongoDB connected to local storage: mongodb://localhost:27018/")
print(f"🗄️  Database: grievance_system")
print(f"📊 Collection: grievances")

def print_grievance_details(grievance_data, operation="Created"):
    """Print grievance details in a structured format"""
    print("\n" + "="*50)
    print(f"GRIEVANCE {operation.upper()}")
    print("="*50)
    print(f"ID: {grievance_data.get('id', grievance_data.get('_id', 'N/A'))}")
    print(f"Name: {grievance_data.get('name', 'N/A')}")
    print(f"Mobile: {grievance_data.get('mobile', 'N/A')}")
    print(f"Complaint: {grievance_data.get('complaint_details', 'N/A')}")
    print(f"Status: {grievance_data.get('status', 'N/A')}")
    print(f"Created At: {grievance_data.get('created_at', 'N/A')}")
    print(f"Updated At: {grievance_data.get('updated_at', 'N/A')}")
    print("="*50 + "\n")

def verify_grievance_storage(grievance_id):
    """Verify that a grievance was properly stored in the database"""
    try:
        print("\n=== VERIFYING GRIEVANCE STORAGE ===")
        
        stored_data = grievances_collection.find_one({'_id': grievance_id})
        
        if stored_data:
            # Convert ObjectId to string for printing and rename _id to id
            stored_data['id'] = str(stored_data['_id'])
            del stored_data['_id']  # Remove the original _id field

            # Verify data integrity
            print("\nVerifying data integrity:")
            print(f"✓ ID exists and matches: {str(stored_data['id']) == str(grievance_id)}")
            print(f"✓ Name is not empty: {bool(stored_data['name'])}")
            print(f"✓ Mobile is valid: {stored_data['mobile'].isdigit() and len(stored_data['mobile']) == 10}")
            print(f"✓ Complaint details exist: {bool(stored_data['complaint_details'])}")
            print(f"✓ Status is set: {bool(stored_data['status'])}")
            print(f"✓ Timestamps are valid: {bool(stored_data['created_at'] and stored_data['updated_at'])}")

            print_grievance_details(stored_data, "Verified")
            return True
        else:
            print("\n❌ VERIFICATION FAILED: Grievance not found in database")
            return False
    except Exception as e:
        print(f"\n❌ VERIFICATION ERROR: {str(e)}")
        return False

def create_grievance(name, mobile, complaint_details):
    try:
        # Input validation
        if not name or not mobile or not complaint_details:
            raise ValueError("All fields are required")
        
        # Validate mobile number format (basic validation)
        if not mobile.isdigit() or len(mobile) != 10:
            raise ValueError("Invalid mobile number format. Please provide a 10-digit number.")
        
        print("\n=== GRIEVANCE REGISTRATION STARTED ===")
        print(f"Name: {name}")
        print(f"Mobile: {mobile}")
        print(f"Complaint: {complaint_details}")
        
        current_time = datetime.now()
        
        # Check if grievance with same mobile number exists
        existing = grievances_collection.find_one({'mobile': mobile})
        
        if existing:
            print(f"\n⚠️ Found existing grievance for mobile: {mobile}")
            print(f"Existing ID: {existing['_id']}")
            print(f"Current Status: {existing['status']}")
            
            # Update existing grievance
            update_result = grievances_collection.update_one(
                {'mobile': mobile},
                {
                    '$set': {
                        'name': name,
                        'complaint_details': complaint_details,
                        'status': 'Pending',
                        'updated_at': current_time
                    }
                }
            )
            grievance_id = existing['_id']
            print("✅ Updated existing grievance")
        else:
            print(f"\n📝 Creating new grievance for mobile: {mobile}")
            # Create new grievance
            grievance_data = {
                'name': name,
                'mobile': mobile,
                'complaint_details': complaint_details,
                'status': 'Pending',
                'created_at': current_time,
                'updated_at': current_time
            }
            result = grievances_collection.insert_one(grievance_data)
            grievance_id = result.inserted_id
            print("✅ Created new grievance")
        
        # Fetch the created/updated grievance
        stored_data = grievances_collection.find_one({'_id': grievance_id})
        
        if stored_data:
            # Convert ObjectId to string for response and rename _id to id
            stored_data['id'] = str(stored_data['_id'])
            del stored_data['_id']  # Remove the original _id field
            stored_data['created_at'] = stored_data['created_at'].isoformat()
            stored_data['updated_at'] = stored_data['updated_at'].isoformat()

            # Print stored data
            print_grievance_details(stored_data, "Stored")

            # Verify storage
            if verify_grievance_storage(grievance_id):
                print("✅ Grievance verification successful")
                return stored_data
            else:
                print("❌ Grievance verification failed")
                return None
        return None
    except Exception as e:
        print(f"\n❌ Error creating grievance: {str(e)}")
        raise

def get_grievance_status(mobile):
    try:
        stored_data = grievances_collection.find_one({'mobile': mobile})
        
        if stored_data:
            # Convert ObjectId to string and datetime to ISO format, rename _id to id
            stored_data['id'] = str(stored_data['_id'])
            del stored_data['_id']  # Remove the original _id field
            stored_data['created_at'] = stored_data['created_at'].isoformat()
            stored_data['updated_at'] = stored_data['updated_at'].isoformat()

            print_grievance_details(stored_data, "Retrieved")
            return stored_data
        return None
    except Exception as e:
        print(f"\nError getting grievance status: {str(e)}")
        return None

def update_grievance_status(grievance_id, status):
    try:
        current_time = datetime.now()
        
        print(f"\nUpdating status for grievance ID: {grievance_id}")
        print(f"New status: {status}")
        
        result = grievances_collection.update_one(
            {'_id': grievance_id},
            {
                '$set': {
                    'status': status,
                    'updated_at': current_time
                }
            }
        )
        
        if result.modified_count > 0:
            stored_data = grievances_collection.find_one({'_id': grievance_id})
            if stored_data:
                # Convert ObjectId to string and datetime to ISO format, rename _id to id
                stored_data['id'] = str(stored_data['_id'])
                del stored_data['_id']  # Remove the original _id field
                stored_data['created_at'] = stored_data['created_at'].isoformat()
                stored_data['updated_at'] = stored_data['updated_at'].isoformat()

                print_grievance_details(stored_data, "Updated")
                return stored_data
        return None
    except Exception as e:
        print(f"\nError updating grievance status: {str(e)}")
        return None

def get_all_grievances():
    try:
        grievances = list(grievances_collection.find())
        
        # Convert ObjectId to string and datetime to ISO format, rename _id to id
        for grievance in grievances:
            grievance['id'] = str(grievance['_id'])
            del grievance['_id']  # Remove the original _id field
            grievance['created_at'] = grievance['created_at'].isoformat()
            grievance['updated_at'] = grievance['updated_at'].isoformat()
        
        print(f"\nRetrieved {len(grievances)} grievances from database")
        return grievances
    except Exception as e:
        print(f"\nError getting all grievances: {str(e)}")
        return [] 