#!/bin/bash

# Script to stop local MongoDB server

echo "🛑 Stopping Local MongoDB Server..."

# Find MongoDB process running on port 27018
PID=$(lsof -ti:27018)

if [ -z "$PID" ]; then
    echo "ℹ️  No MongoDB process found running on port 27018"
else
    echo "📍 Found MongoDB process with PID: $PID"
    
    # Try graceful shutdown first
    echo "Attempting graceful shutdown..."
    kill -TERM $PID
    
    # Wait a few seconds
    sleep 3
    
    # Check if process is still running
    if kill -0 $PID 2>/dev/null; then
        echo "⚠️  Process still running, forcing shutdown..."
        kill -KILL $PID
        sleep 1
    fi
    
    # Verify shutdown
    if ! kill -0 $PID 2>/dev/null; then
        echo "✅ MongoDB stopped successfully"
    else
        echo "❌ Failed to stop MongoDB"
        exit 1
    fi
fi

# Clean up PID file if it exists
if [ -f "./mongodb_data/mongod.pid" ]; then
    rm -f "./mongodb_data/mongod.pid"
    echo "🧹 Cleaned up PID file"
fi

echo "✅ Local MongoDB shutdown complete"
