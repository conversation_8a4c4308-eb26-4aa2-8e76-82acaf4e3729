#!/usr/bin/env python3
"""
Complete setup script for local MongoDB storage in current directory
"""

import os
import subprocess
import time
from pymongo import MongoClient

def check_mongodb_installed():
    """Check if MongoDB is installed"""
    try:
        result = subprocess.run(['mongod', '--version'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def create_directories():
    """Create necessary directories for local MongoDB"""
    directories = [
        'mongodb_data/db',
        'mongodb_data/logs'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def start_local_mongodb():
    """Start local MongoDB instance"""
    print("🚀 Starting local MongoDB...")
    
    # Check if already running
    try:
        client = MongoClient('mongodb://localhost:27018/', serverSelectionTimeoutMS=2000)
        client.server_info()
        client.close()
        print("ℹ️  Local MongoDB is already running on port 27018")
        return True
    except:
        pass
    
    # Start MongoDB
    try:
        process = subprocess.Popen([
            'mongod', 
            '--config', 'mongodb_local.conf'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for MongoDB to start
        print("⏳ Waiting for MongoDB to start...")
        time.sleep(5)
        
        # Check if it started successfully
        try:
            client = MongoClient('mongodb://localhost:27018/', serverSelectionTimeoutMS=5000)
            client.server_info()
            client.close()
            print("✅ Local MongoDB started successfully!")
            return True
        except:
            print("❌ Failed to start MongoDB")
            return False
            
    except Exception as e:
        print(f"❌ Error starting MongoDB: {str(e)}")
        return False

def show_setup_complete():
    """Show setup completion message"""
    print("\n🎉 LOCAL MONGODB SETUP COMPLETE!")
    print("=" * 60)
    print("📂 Data Storage Location:")
    print(f"   {os.path.abspath('mongodb_data/db')}")
    print()
    print("🔗 Connection Details:")
    print("   Host: localhost")
    print("   Port: 27018")
    print("   Database: grievance_system")
    print("   Collection: grievances")
    print("   Connection String: mongodb://localhost:27018/")
    print()
    print("📝 Log Files:")
    print(f"   {os.path.abspath('mongodb_data/logs/mongod.log')}")
    print()
    print("🔧 Management Scripts:")
    print("   Start MongoDB: ./start_local_mongodb.sh")
    print("   Stop MongoDB:  ./stop_local_mongodb.sh")
    print("   Migrate Data:  python migrate_data_to_local.py")
    print()
    print("⚠️  IMPORTANT NOTES:")
    print("   • Your application is now configured to use port 27018")
    print("   • All data will be stored in the current project directory")
    print("   • Make sure to run migration script if you have existing data")
    print("   • Keep MongoDB running while using the application")

def main():
    print("🔧 SETTING UP LOCAL MONGODB STORAGE")
    print("=" * 60)
    print(f"📂 Current Directory: {os.getcwd()}")
    print()
    
    # Check if MongoDB is installed
    if not check_mongodb_installed():
        print("❌ MongoDB is not installed!")
        print("Please install MongoDB first:")
        print("   brew install mongodb-community")
        return False
    
    print("✅ MongoDB is installed")
    
    # Create directories
    print("\n📁 Creating directories...")
    create_directories()
    
    # Start local MongoDB
    print("\n🚀 Starting local MongoDB...")
    if not start_local_mongodb():
        print("❌ Failed to start local MongoDB")
        return False
    
    # Show completion message
    show_setup_complete()
    
    return True

if __name__ == "__main__":
    if main():
        print("\n✅ Setup completed successfully!")
        print("You can now run your application with local MongoDB storage.")
    else:
        print("\n❌ Setup failed!")
        print("Please check the error messages above and try again.")
