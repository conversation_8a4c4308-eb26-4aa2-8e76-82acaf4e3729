# 📂 Local MongoDB Storage Setup

This setup configures MongoDB to store all data in your current project directory instead of the system-wide location.

## 🗂️ Directory Structure

```
your-project/
├── mongodb_data/
│   ├── db/           # MongoDB database files
│   └── logs/         # MongoDB log files
├── mongodb_local.conf    # MongoDB configuration
├── start_local_mongodb.sh    # Start script
├── stop_local_mongodb.sh     # Stop script
├── setup_local_mongodb.py    # Setup script
└── migrate_data_to_local.py  # Data migration script
```

## 🚀 Quick Start

### 1. Run the Setup Script
```bash
python setup_local_mongodb.py
```

### 2. Migrate Existing Data (if any)
```bash
python migrate_data_to_local.py
```

### 3. Start Your Application
```bash
# Start the API server
uvicorn api:app --reload

# Start the Streamlit app
streamlit run app.py
```

## 🔧 Manual Management

### Start MongoDB
```bash
./start_local_mongodb.sh
```

### Stop MongoDB
```bash
./stop_local_mongodb.sh
```

### Check Status
```bash
# Check if MongoDB is running
lsof -i :27018

# View logs
tail -f mongodb_data/logs/mongod.log
```

## 📊 Connection Details

- **Host:** localhost
- **Port:** 27018 (different from system MongoDB on 27017)
- **Database:** grievance_system
- **Collection:** grievances
- **Connection String:** `mongodb://localhost:27018/`

## 📂 Data Location

All your MongoDB data is now stored in:
```
./mongodb_data/db/
```

This means:
- ✅ Data is portable with your project
- ✅ No system-wide installation conflicts
- ✅ Easy backup (just copy the folder)
- ✅ Version control friendly (add to .gitignore)

## 💾 Backup & Restore

### Create Backup
```bash
# Copy the entire data directory
cp -r mongodb_data mongodb_data_backup_$(date +%Y%m%d)

# Or export to JSON
mongoexport --port 27018 --db grievance_system --collection grievances --out backup.json
```

### Restore from Backup
```bash
# Restore from directory backup
cp -r mongodb_data_backup_20240615 mongodb_data

# Or import from JSON
mongoimport --port 27018 --db grievance_system --collection grievances --file backup.json
```

## 🔍 Accessing Data

### Using MongoDB Shell
```bash
mongo --port 27018
use grievance_system
db.grievances.find().pretty()
```

### Using Python
```python
from pymongo import MongoClient
client = MongoClient('mongodb://localhost:27018/')
db = client['grievance_system']
collection = db['grievances']
```

### Using Your Web App
- The application is automatically configured to use port 27018
- Admin panel: http://localhost:8501 (login: admin/admin123)
- API: http://localhost:8000

## ⚠️ Important Notes

1. **Port Change:** MongoDB now runs on port 27018 instead of 27017
2. **Local Storage:** All data is in `./mongodb_data/db/`
3. **Logs:** Check `./mongodb_data/logs/mongod.log` for issues
4. **Backup:** Regular backups are recommended
5. **Git:** Add `mongodb_data/` to `.gitignore` to avoid committing data

## 🐛 Troubleshooting

### MongoDB Won't Start
```bash
# Check if port is already in use
lsof -i :27018

# Check logs
cat mongodb_data/logs/mongod.log

# Try manual start
mongod --config mongodb_local.conf
```

### Connection Issues
```bash
# Test connection
mongo --port 27018 --eval "db.runCommand('ping')"

# Check if service is running
ps aux | grep mongod
```

### Data Migration Issues
```bash
# Check source data
mongo --port 27017 --eval "use grievance_system; db.grievances.count()"

# Check target data
mongo --port 27018 --eval "use grievance_system; db.grievances.count()"
```

## 📋 File Descriptions

- **`mongodb_local.conf`**: MongoDB configuration for local storage
- **`start_local_mongodb.sh`**: Script to start MongoDB with local config
- **`stop_local_mongodb.sh`**: Script to stop local MongoDB
- **`setup_local_mongodb.py`**: Complete setup automation
- **`migrate_data_to_local.py`**: Migrate existing data to local storage
- **`mongodb_data/`**: Directory containing all MongoDB files

Your data is now safely stored in your project directory! 🎉
