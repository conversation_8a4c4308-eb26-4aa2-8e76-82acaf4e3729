import os
from dotenv import load_dotenv

load_dotenv()

# Try to import Groq, but provide fallback if it fails
try:
    from groq import Groq
    client = Groq(api_key=os.getenv("GROQ_API_KEY"))
    GROQ_AVAILABLE = True
    print("Groq client initialized successfully")
except Exception as e:
    print(f"Groq not available, using fallback: {e}")
    client = None
    GROQ_AVAILABLE = False

SYSTEM_PROMPT = """You are an intelligent grievance registration assistant. Your role is to:
1. Analyze user queries and respond appropriately based on their intent
2. Help users register grievances by collecting necessary information
3. Provide status updates for existing grievances
4. Handle general questions about the grievance process
5. Maintain a professional and empathetic tone

QUERY ANALYSIS RULES:
1. STATUS QUERIES: If user asks about status, tracking, or checking complaints
   - Ask for mobile number if not provided
   - Explain how to check status

2. REGISTRATION QUERIES: If user wants to register/file a complaint
   - Collect name, mobile, and complaint details
   - Guide them through the process naturally

3. GENERAL QUESTIONS: If user asks how the system works, what they can do, etc.
   - Provide helpful information about available services
   - Guide them to appropriate actions

4. INFORMATION QUERIES: If user asks about specific procedures, timelines, etc.
   - Provide accurate information about the grievance process
   - Be helpful and informative

INFORMATION EXTRACTION RULES:
- Extract names from phrases like "My name is John", "I am Sarah", "This is Mike calling"
- Extract mobile numbers from any 10-digit number mentioned
- Extract complaints from descriptions of problems, issues, or concerns
- Be intelligent about context - if someone says "I have a water problem", that's a complaint

RESPONSE FORMAT RULES:
When you have collected all three pieces of information (name, mobile, complaint), you MUST format your response to include these exact lines:

Name: [extracted name]
Mobile: [extracted mobile number]
Complaint: [extracted complaint details]

CONVERSATION EXAMPLES:

STATUS CHECK:
User: "What's the status of my complaint?"
You: "I can help you check your complaint status. Please provide your mobile number that was used during registration."

User: "How can I track my grievance?"
You: "To track your grievance, I need your mobile number. Once you provide it, I can check the current status of your complaint."

REGISTRATION:
User: "I want to file a complaint"
You: "I'll help you register your complaint. Please provide:
1. Your name
2. Your mobile number
3. Details about the issue you're facing"

GENERAL QUESTIONS:
User: "How does this system work?"
You: "This grievance system allows you to:
1. Register new complaints about civic issues
2. Track the status of your existing complaints
3. Get updates on resolution progress

Would you like to register a new complaint or check the status of an existing one?"

Always analyze the user's intent first, then respond appropriately."""

def extract_information_from_text(text):
    """Extract name, mobile, and complaint from natural text"""
    import re

    # Handle case where text might not be a string
    if not isinstance(text, str):
        return None, None, None

    name = None
    mobile = None
    complaint = None

    # Extract mobile number (10 digits)
    mobile_pattern = r'\b\d{10}\b'
    mobile_match = re.search(mobile_pattern, text)
    if mobile_match:
        mobile = mobile_match.group()

    # Extract name patterns (more comprehensive)
    name_patterns = [
        r'my name is ([a-zA-Z\s]{2,30})(?:\s+and|\s*,|\s*\.|\s*$|\s+my|\s+i)',
        r'i am ([a-zA-Z\s]{2,30})(?:\s+and|\s*,|\s*\.|\s*$|\s+my|\s+i)',
        r'this is ([a-zA-Z\s]{2,30})(?:\s+calling|\s+speaking|\s*,|\s*\.|\s*$|\s+and)',
        r'name:\s*([a-zA-Z\s]{2,30})(?:\s*,|\s*\.|\s*$|\s+mobile|\s+phone)',
        r'i\'m ([a-zA-Z\s]{2,30})(?:\s+and|\s*,|\s*\.|\s*$|\s+my)',
        r'hello,?\s*i\'m ([a-zA-Z\s]{2,30})(?:\s+and|\s*,|\s*\.|\s*$)',
        r'hi,?\s*my name is ([a-zA-Z\s]{2,30})(?:\s+and|\s*,|\s*\.|\s*$)',
        r'hi,?\s*i am ([a-zA-Z\s]{2,30})(?:\s+and|\s*,|\s*\.|\s*$)'
    ]

    for pattern in name_patterns:
        match = re.search(pattern, text.lower())
        if match:
            extracted_name = match.group(1).strip()
            # Clean up the name - remove common words that might be captured
            words_to_remove = ['and', 'have', 'has', 'with', 'having', 'facing', 'experiencing', 'my', 'mobile', 'phone', 'number']
            name_words = extracted_name.split()
            clean_name_words = []
            for word in name_words:
                if word.lower() not in words_to_remove and len(word) > 1:
                    clean_name_words.append(word)
                else:
                    break  # Stop at first unwanted word
            if clean_name_words and len(' '.join(clean_name_words)) >= 2:
                name = ' '.join(clean_name_words).title()
                break

    # Extract complaint (look for problem descriptions)
    complaint_keywords = [
        'problem', 'issue', 'complaint', 'trouble', 'not working', 'broken', 'damaged',
        'outage', 'supply', 'repair', 'fix', 'water', 'electricity', 'road', 'garbage',
        'drainage', 'leakage', 'fault', 'disruption', 'service', 'maintenance', 'blocked',
        'overflow', 'shortage', 'cut', 'failure', 'defect', 'malfunction'
    ]

    # Check if the text contains complaint-related content
    text_lower = text.lower()
    if any(keyword in text_lower for keyword in complaint_keywords):
        # If it's a complaint, use the whole message as complaint context
        complaint = text.strip()
    elif len(text.split()) > 5 and not any(greeting in text_lower for greeting in ['hello', 'hi', 'hey']):
        # If it's a longer message without greetings, might be a complaint description
        complaint = text.strip()

    return name, mobile, complaint

def analyze_user_intent(user_message):
    """Analyze user message to determine intent"""
    user_message_lower = user_message.lower().strip()

    # Handle empty or very short messages
    if not user_message_lower or len(user_message_lower) < 2:
        return "general"

    # Status checking queries (more comprehensive)
    status_keywords = [
        "status", "check", "track", "tracking", "progress", "update",
        "what's the status", "how is my", "where is my", "status of my complaint",
        "complaint status", "grievance status", "my complaint", "my grievance",
        "check my", "track my", "status for", "check status"
    ]

    # Registration/complaint filing queries (more comprehensive)
    register_keywords = [
        "register", "file", "submit", "complaint", "grievance", "problem",
        "issue", "trouble", "report", "lodge", "new complaint", "want to complain",
        "i have a problem", "there is a problem", "facing issue", "want to file",
        "need to register", "water problem", "electricity issue", "road problem",
        "garbage issue", "drainage problem", "my name is", "i am", "this is"
    ]

    # General information queries
    info_keywords = [
        "how does", "how to", "what can", "what is", "explain", "help me understand",
        "how this works", "what services", "what do you do", "about this system",
        "how can you help", "what can you do"
    ]

    # Process/procedure queries
    process_keywords = [
        "process", "procedure", "steps", "how long", "timeline", "what happens next",
        "how much time", "when will", "resolution time", "take to resolve", "time to resolve"
    ]

    # Check for mobile number patterns (might indicate status check)
    import re
    mobile_pattern = r'\b\d{10}\b'
    has_mobile = re.search(mobile_pattern, user_message)

    # Check in order of priority - more specific first
    if any(keyword in user_message_lower for keyword in process_keywords):
        return "process_info"
    elif any(keyword in user_message_lower for keyword in status_keywords):
        return "status_check"
    elif has_mobile and len(user_message_lower.split()) <= 3:  # Just mobile number or short message with mobile
        return "status_check"
    elif any(keyword in user_message_lower for keyword in info_keywords):
        return "general_info"
    elif any(keyword in user_message_lower for keyword in register_keywords):
        return "registration"
    else:
        # If message contains personal information patterns, likely registration
        if ("my name is" in user_message_lower or
            "i am" in user_message_lower or
            "this is" in user_message_lower or
            has_mobile):
            return "registration"
        return "general"

def get_chat_response(messages):
    # Always use fallback logic for now since Groq has issues
    import re  # Import re module for regex operations

    # Enhanced fallback response with intelligent query analysis
    user_message = messages[-1]["content"] if messages else ""

    print(f"\n=== PROCESSING USER MESSAGE ===")
    print(f"User message: {user_message}")
    print(f"Total messages in conversation: {len(messages)}")

    # Analyze user intent first
    intent = analyze_user_intent(user_message)
    print(f"Detected intent: {intent}")

    # Handle different intents
    if intent == "status_check":
        # Check if mobile number is provided
        mobile_match = re.search(r'\b\d{10}\b', user_message)
        if mobile_match:
            print(f"Found mobile number for status check: {mobile_match.group()}")
            return f"I'll check the status for mobile number {mobile_match.group()}. Please wait while I retrieve your complaint information."
        else:
            print("Status check requested but no mobile number provided")
            return """I can help you check your complaint status. Please provide your mobile number that was used during registration.

For example: "Check status for 9876543210" or just provide your 10-digit mobile number."""

    elif intent == "general_info":
        print("Providing general information about the system")
        return """This grievance registration system helps you with:

🔹 **Register New Complaints**: File complaints about civic issues like water supply, electricity, roads, garbage collection, etc.

🔹 **Track Complaint Status**: Check the progress of your registered complaints using your mobile number

🔹 **Get Updates**: Receive updates on complaint resolution progress

🔹 **Admin Support**: Complaints are reviewed and updated by administrators

**To get started:**
- Say "I want to file a complaint" to register a new grievance
- Say "Check my complaint status" to track existing complaints
- Provide your mobile number to check status

How can I assist you today?"""

    elif intent == "process_info":
        print("Providing process information")
        return """**Grievance Process Information:**

📋 **Registration Process:**
1. Provide your name, mobile number, and complaint details
2. System generates a unique complaint ID
3. Complaint is assigned "Pending" status initially

⏱️ **Timeline:**
- Complaints are typically reviewed within 24-48 hours
- Status updates: Pending → In Progress → Resolved → Closed
- Resolution time varies based on complaint type and complexity

📱 **Tracking:**
- Use your mobile number to check status anytime
- You'll see current status and timestamps

**Need to file a complaint or check status?**"""

    # Check if user just provided a mobile number (might be for status check)
    mobile_only_match = re.search(r'^\s*\d{10}\s*$', user_message)
    if mobile_only_match and len(messages) > 1:
        # Check if previous conversation was about status
        for msg in messages[:-1]:
            if (isinstance(msg, dict) and msg.get('role') == 'assistant' and
                ('complaint status' in msg.get('content', '').lower() or
                 'provide your mobile number' in msg.get('content', '').lower())):
                print(f"User provided mobile number for status check: {mobile_only_match.group().strip()}")
                return f"I'll check the status for mobile number {mobile_only_match.group().strip()}. Please wait while I retrieve your complaint information."

    # For registration intent or when we have partial information
    if intent == "registration" or intent == "general":
        print("Processing registration or general intent")

        # Extract information from current message
        name, mobile, complaint = extract_information_from_text(user_message)
        print(f"Extracted from current message - Name: {name}, Mobile: {mobile}, Complaint: {complaint}")

        # Also check previous USER messages for context
        collected_name = None
        collected_mobile = None
        collected_complaint = None

        for msg in messages[:-1]:
            if isinstance(msg, dict) and msg.get('role') == 'user':
                msg_content = msg.get('content', '')
                if isinstance(msg_content, str):
                    prev_name, prev_mobile, prev_complaint = extract_information_from_text(msg_content)
                    if prev_name and not collected_name:
                        collected_name = prev_name
                    if prev_mobile and not collected_mobile:
                        collected_mobile = prev_mobile
                    if prev_complaint and not collected_complaint:
                        collected_complaint = prev_complaint

        # Use current extraction or previous collection
        final_name = name or collected_name
        final_mobile = mobile or collected_mobile
        final_complaint = complaint or collected_complaint

        print(f"Final extracted info - Name: {final_name}, Mobile: {final_mobile}, Complaint: {final_complaint}")

        # Check if we have all information for registration
        if final_name and final_mobile and final_complaint:
            print("All information collected, ready for registration")
            return f"""Perfect! I have collected all the information needed to register your grievance:

Name: {final_name}
Mobile: {final_mobile}
Complaint: {final_complaint}

Your complaint will be registered and you'll receive a unique complaint ID for tracking."""

        # Partial information - ask for what's missing
        elif final_name and final_mobile and not final_complaint:
            print("Have name and mobile, asking for complaint details")
            return f"Thank you {final_name}! I have your name and mobile number ({final_mobile}). Please describe the issue or problem you'd like to report in detail."

        elif final_name and final_complaint and not final_mobile:
            print("Have name and complaint, asking for mobile")
            return f"Thank you {final_name}! I understand your concern. To register your complaint, please provide your mobile number (10 digits)."

        elif final_mobile and final_complaint and not final_name:
            print("Have mobile and complaint, asking for name")
            return f"I have your mobile number ({final_mobile}) and understand the issue. Please tell me your full name to complete the registration."

        elif final_name and not final_mobile and not final_complaint:
            print("Have only name, asking for mobile and complaint")
            return f"Hello {final_name}! To register your grievance, I need:\n1. Your mobile number (10 digits)\n2. Details about the issue you're facing"

        elif final_mobile and not final_name and not final_complaint:
            print("Have only mobile, asking for name and complaint")
            return f"I have your mobile number ({final_mobile}). Please provide:\n1. Your full name\n2. Details about the issue you'd like to report"

        elif final_complaint and not final_name and not final_mobile:
            print("Have only complaint, asking for name and mobile")
            return f"I understand you're facing this issue. To register your complaint, I need:\n1. Your full name\n2. Your mobile number (10 digits)"

        # No information yet - provide guidance based on intent
        elif intent == "registration":
            print("Registration intent but no information collected yet")
            return """I'll help you register a complaint. Please provide the following information:

📝 **Required Details:**
1. **Your Name**: Full name for registration
2. **Mobile Number**: 10-digit mobile number for tracking
3. **Complaint Details**: Describe the issue you're facing

**Example:** "Hi, I'm John Smith, my mobile is 9876543210, and there's no water supply in my area for the past 3 days."

You can provide this information all at once or step by step. How would you like to proceed?"""

        else:
            print("General intent, providing welcome message")
            return """Hello! I'm your grievance registration assistant. I can help you with:

🔹 **File a New Complaint** - Register civic issues like water, electricity, roads, etc.
🔹 **Check Complaint Status** - Track your existing complaints using mobile number

**To get started:**
- Say "I want to file a complaint" or describe your problem
- Say "Check my complaint status" and provide your mobile number

What would you like to do today?"""

    # Default fallback - should rarely be reached now
    print("Reached default fallback")
    return """Hello! I'm your grievance registration assistant. I can help you with:

🔹 **File a New Complaint** - Register civic issues like water, electricity, roads, etc.
🔹 **Check Complaint Status** - Track your existing complaints using mobile number

**To get started:**
- Say "I want to file a complaint" or describe your problem
- Say "Check my complaint status" and provide your mobile number

What would you like to do today?"""



def process_user_input(user_input, conversation_history):
    """Process user input with conversation context"""
    # Build conversation messages with proper role alternation
    messages = []

    # Add conversation history
    for i, msg in enumerate(conversation_history):  # Include all conversation history
        if i % 2 == 0:
            messages.append({"role": "user", "content": msg})
        else:
            messages.append({"role": "assistant", "content": msg})

    # Add current user input
    messages.append({"role": "user", "content": user_input})

    # Get response from LLM or fallback
    response = get_chat_response(messages)

    # Ensure we never return None
    if response is None:
        response = "I apologize, but I'm having trouble processing your request. Please try again."

    return response