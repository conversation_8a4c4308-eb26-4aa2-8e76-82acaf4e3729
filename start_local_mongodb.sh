#!/bin/bash

# Script to start MongoDB with local data storage in current directory

echo "🚀 Starting Local MongoDB Server..."
echo "📂 Data will be stored in: $(pwd)/mongodb_data/db"
echo "📝 Logs will be stored in: $(pwd)/mongodb_data/logs"
echo "🌐 MongoDB will run on port: 27018"
echo ""

# Check if MongoDB is installed
if ! command -v mongod &> /dev/null; then
    echo "❌ MongoDB is not installed or not in PATH"
    echo "Please install MongoDB first:"
    echo "  brew install mongodb-community"
    exit 1
fi

# Create directories if they don't exist
mkdir -p mongodb_data/db mongodb_data/logs

# Check if MongoDB is already running on port 27018
if lsof -Pi :27018 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  MongoDB is already running on port 27018"
    echo "Use 'stop_local_mongodb.sh' to stop it first"
    exit 1
fi

# Start MongoDB with local configuration
echo "Starting MongoDB with local configuration..."
mongod --config mongodb_local.conf &

# Wait a moment for MongoDB to start
sleep 3

# Check if MongoDB started successfully
if lsof -Pi :27018 -sTCP:LISTEN -t >/dev/null ; then
    echo "✅ Local MongoDB started successfully!"
    echo ""
    echo "📊 Connection Details:"
    echo "   Host: localhost"
    echo "   Port: 27018"
    echo "   Connection String: mongodb://localhost:27018/"
    echo ""
    echo "🔧 To connect using MongoDB shell:"
    echo "   mongo --port 27018"
    echo ""
    echo "📂 Data Location: $(pwd)/mongodb_data/db"
    echo "📝 Log File: $(pwd)/mongodb_data/logs/mongod.log"
    echo ""
    echo "⚠️  Keep this terminal open or MongoDB will stop!"
    echo "   Use Ctrl+C to stop MongoDB"
else
    echo "❌ Failed to start MongoDB"
    echo "Check the log file: $(pwd)/mongodb_data/logs/mongod.log"
    exit 1
fi

# Keep the script running
wait
