#!/usr/bin/env python3
"""
Script to migrate existing MongoDB data from system installation to local project directory
"""

from pymongo import MongoClient
import json
from datetime import datetime

def migrate_data():
    """Migrate data from system MongoDB to local MongoDB"""
    print("🔄 MIGRATING MONGODB DATA TO LOCAL STORAGE")
    print("=" * 60)
    
    # Source connection (system MongoDB on port 27017)
    source_client = None
    target_client = None
    
    try:
        print("📡 Connecting to source MongoDB (port 27017)...")
        source_client = MongoClient('mongodb://localhost:27017/')
        source_db = source_client['grievance_system']
        source_collection = source_db['grievances']
        
        # Get all data from source
        print("📊 Fetching data from source database...")
        source_data = list(source_collection.find())
        print(f"   Found {len(source_data)} grievances to migrate")
        
        if not source_data:
            print("ℹ️  No data found in source database")
            return True
        
        # Target connection (local MongoDB on port 27018)
        print("📡 Connecting to target MongoDB (port 27018)...")
        target_client = MongoClient('mongodb://localhost:27018/')
        target_db = target_client['grievance_system']
        target_collection = target_db['grievances']
        
        # Clear existing data in target (if any)
        existing_count = target_collection.count_documents({})
        if existing_count > 0:
            print(f"⚠️  Found {existing_count} existing documents in target, clearing...")
            target_collection.delete_many({})
        
        # Insert data into target
        print("📥 Inserting data into local database...")
        result = target_collection.insert_many(source_data)
        
        print(f"✅ Successfully migrated {len(result.inserted_ids)} grievances")
        
        # Verify migration
        print("🔍 Verifying migration...")
        target_count = target_collection.count_documents({})
        
        if target_count == len(source_data):
            print(f"✅ Migration verified: {target_count} documents in local database")
            
            # Show migrated data
            print("\n📋 MIGRATED GRIEVANCES:")
            print("-" * 40)
            for i, doc in enumerate(target_collection.find(), 1):
                print(f"{i}. {doc.get('name', 'N/A')} - {doc.get('mobile', 'N/A')} - {doc.get('status', 'N/A')}")
            
            return True
        else:
            print(f"❌ Migration verification failed: Expected {len(source_data)}, got {target_count}")
            return False
            
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        return False
    
    finally:
        if source_client:
            source_client.close()
        if target_client:
            target_client.close()

def backup_existing_data():
    """Create a backup of existing data before migration"""
    print("💾 Creating backup of existing data...")
    
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['grievance_system']
        collection = db['grievances']
        
        data = list(collection.find())
        
        if data:
            # Convert ObjectId to string for JSON serialization
            for doc in data:
                doc['_id'] = str(doc['_id'])
                if isinstance(doc.get('created_at'), datetime):
                    doc['created_at'] = doc['created_at'].isoformat()
                if isinstance(doc.get('updated_at'), datetime):
                    doc['updated_at'] = doc['updated_at'].isoformat()
            
            backup_filename = f"grievances_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(backup_filename, 'w') as f:
                json.dump(data, f, indent=2)
            
            print(f"✅ Backup created: {backup_filename}")
            return backup_filename
        else:
            print("ℹ️  No data to backup")
            return None
            
    except Exception as e:
        print(f"❌ Backup failed: {str(e)}")
        return None
    
    finally:
        client.close()

def check_local_mongodb():
    """Check if local MongoDB is running"""
    try:
        client = MongoClient('mongodb://localhost:27018/', serverSelectionTimeoutMS=2000)
        client.server_info()
        client.close()
        return True
    except:
        return False

if __name__ == "__main__":
    print("🔄 MONGODB DATA MIGRATION TOOL")
    print("=" * 60)
    
    # Check if local MongoDB is running
    if not check_local_mongodb():
        print("❌ Local MongoDB is not running on port 27018")
        print("Please start it first using: ./start_local_mongodb.sh")
        exit(1)
    
    print("✅ Local MongoDB is running")
    
    # Create backup
    backup_file = backup_existing_data()
    
    # Migrate data
    if migrate_data():
        print("\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("📂 Your data is now stored locally in: ./mongodb_data/db")
        print("🔗 Connection string: mongodb://localhost:27018/")
        print("📝 Database: grievance_system")
        print("📊 Collection: grievances")
        
        if backup_file:
            print(f"💾 Backup file: {backup_file}")
        
        print("\n⚠️  IMPORTANT: Update your application to use port 27018")
        
    else:
        print("\n❌ MIGRATION FAILED!")
        print("Please check the error messages above and try again.")
