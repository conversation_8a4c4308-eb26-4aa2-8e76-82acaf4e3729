#!/usr/bin/env python3
"""
Test script to verify the chatbot functionality
"""

from llm_integration import process_user_input, analyze_user_intent, extract_information_from_text

def test_intent_detection():
    """Test intent detection functionality"""
    print("=== TESTING INTENT DETECTION ===")
    
    test_cases = [
        ("Hello", "general"),
        ("I want to file a complaint", "registration"),
        ("Check my complaint status", "status_check"),
        ("What is this system about?", "general_info"),
        ("How long does the process take?", "process_info"),
        ("My name is <PERSON>", "registration"),
        ("9876543210", "status_check"),
        ("I have a water problem", "registration"),
    ]
    
    for message, expected_intent in test_cases:
        detected_intent = analyze_user_intent(message)
        status = "✅ PASS" if detected_intent == expected_intent else "❌ FAIL"
        print(f"{status} | '{message}' -> Expected: {expected_intent}, Got: {detected_intent}")

def test_information_extraction():
    """Test information extraction functionality"""
    print("\n=== TESTING INFORMATION EXTRACTION ===")
    
    test_cases = [
        ("My name is <PERSON>", ("<PERSON>", None, None)),
        ("Hi, I'm <PERSON> and my mobile is 9876543210", ("<PERSON>", "9876543210", None)),
        ("I have a water problem in my area", (None, None, "I have a water problem in my area")),
        ("My name is Mike, mobile 8765432109, water supply issue", ("Mike", "8765432109", "My name is Mike, mobile 8765432109, water supply issue")),
    ]
    
    for message, expected in test_cases:
        result = extract_information_from_text(message)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"{status} | '{message}'")
        print(f"    Expected: {expected}")
        print(f"    Got:      {result}")

def test_conversation_flow():
    """Test conversation flow"""
    print("\n=== TESTING CONVERSATION FLOW ===")
    
    # Test 1: Registration flow
    print("\n--- Test 1: Registration Flow ---")
    conversation = []
    
    # Step 1: Initial greeting
    response1 = process_user_input("Hello", conversation)
    conversation.extend(["Hello", response1])
    print(f"User: Hello")
    print(f"Bot: {response1[:100]}...")
    
    # Step 2: Registration intent
    response2 = process_user_input("I want to file a complaint", conversation)
    conversation.extend(["I want to file a complaint", response2])
    print(f"User: I want to file a complaint")
    print(f"Bot: {response2[:100]}...")
    
    # Step 3: Provide all information
    response3 = process_user_input("My name is John Smith, mobile 9876543210, water supply issue", conversation)
    conversation.extend(["My name is John Smith, mobile 9876543210, water supply issue", response3])
    print(f"User: My name is John Smith, mobile 9876543210, water supply issue")
    print(f"Bot: {response3[:100]}...")
    
    # Test 2: Status check flow
    print("\n--- Test 2: Status Check Flow ---")
    conversation2 = []
    
    # Step 1: Status check request
    response1 = process_user_input("Check my complaint status", conversation2)
    conversation2.extend(["Check my complaint status", response1])
    print(f"User: Check my complaint status")
    print(f"Bot: {response1[:100]}...")
    
    # Step 2: Provide mobile number
    response2 = process_user_input("9876543210", conversation2)
    conversation2.extend(["9876543210", response2])
    print(f"User: 9876543210")
    print(f"Bot: {response2[:100]}...")

if __name__ == "__main__":
    print("🤖 CHATBOT FUNCTIONALITY TEST")
    print("=" * 50)
    
    test_intent_detection()
    test_information_extraction()
    test_conversation_flow()
    
    print("\n" + "=" * 50)
    print("✅ Test completed! Check the results above.")
